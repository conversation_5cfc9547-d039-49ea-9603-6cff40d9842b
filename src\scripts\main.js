// Main JavaScript for Gas Management System

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    updateDateTime();
    setupNavigation();
    loadStatistics();
    
    // Update time every second
    setInterval(updateDateTime, 1000);
}

// Update Date and Time
function updateDateTime() {
    const now = new Date();
    const dateElement = document.getElementById('current-date');
    const timeElement = document.getElementById('current-time');
    
    if (dateElement && timeElement) {
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        
        dateElement.textContent = now.toLocaleDateString('ar-EG', options);
        timeElement.textContent = now.toLocaleTimeString('ar-EG');
    }
}

// Setup Navigation
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const contentSections = document.querySelectorAll('.content-section');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetSection = this.getAttribute('data-section');
            
            // Remove active class from all nav items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Hide all content sections
            contentSections.forEach(section => section.classList.remove('active'));
            
            // Show target section
            const targetElement = document.getElementById(targetSection);
            if (targetElement) {
                targetElement.classList.add('active');
            }
        });
    });
}

// Load Statistics (Mock Data)
async function loadStatistics() {
    try {
        // In a real app, this would be an API call
        const stats = {
            sales: { total: 5000, today: 2000 },
            gasCards: { total: 150, active: 120 },
            appointments: { today: 8, upcoming: 25 },
            customers: { total: 300, new: 15 },
            suppliers: { total: 45, active: 40 },
            inventory: { items: 250, lowStock: 12 },
            purchases: { today: 1500, month: 45000 },
            debts: { total: 25000, overdue: 5000 },
            certificates: { valid: 180, expiring: 15 }
        };
        
        updateStatisticsCards(stats);
    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

// Update Statistics Cards
function updateStatisticsCards(stats) {
    // Update sales card
    const salesCard = document.querySelector('.stat-card.sales');
    if (salesCard) {
        const mainValue = salesCard.querySelector('.main-value');
        const subValue = salesCard.querySelector('.sub-value');
        if (mainValue) mainValue.textContent = `${stats.sales.total.toLocaleString()} ج.م`;
        if (subValue) subValue.textContent = `مبيعات اليوم: ${stats.sales.today.toLocaleString()} ج.م`;
    }
    
    // Update items card
    const itemsCard = document.querySelector('.stat-card.items');
    if (itemsCard) {
        const mainValue = itemsCard.querySelector('.main-value');
        if (mainValue) mainValue.textContent = stats.inventory.items;
    }
    
    // Update appointments card
    const appointmentsCard = document.querySelector('.stat-card.appointments');
    if (appointmentsCard) {
        const mainValue = appointmentsCard.querySelector('.main-value');
        if (mainValue) mainValue.textContent = stats.appointments.today;
    }
    
    // Update suppliers card
    const suppliersCard = document.querySelector('.stat-card.suppliers');
    if (suppliersCard) {
        const mainValue = suppliersCard.querySelector('.main-value');
        const subValue = suppliersCard.querySelector('.sub-value');
        if (mainValue) mainValue.textContent = `إجمالي الموردين: ${stats.suppliers.total}`;
        if (subValue) subValue.textContent = `موردين نشطين: ${stats.suppliers.active}`;
    }
    
    // Update customers card
    const customersCard = document.querySelector('.stat-card.customers');
    if (customersCard) {
        const mainValue = customersCard.querySelector('.main-value');
        if (mainValue) mainValue.textContent = `زبائن جدد هذا الشهر: ${stats.customers.new}`;
    }
    
    // Update debts card
    const debtsCard = document.querySelector('.stat-card.debts');
    if (debtsCard) {
        const mainValue = debtsCard.querySelector('.main-value');
        if (mainValue) mainValue.textContent = `ديون مؤجلة: ${stats.debts.overdue.toLocaleString()} ج.م`;
    }
    
    // Update purchases card
    const purchasesCard = document.querySelector('.stat-card.purchases');
    if (purchasesCard) {
        const mainValue = purchasesCard.querySelector('.main-value');
        const subValue = purchasesCard.querySelector('.sub-value');
        if (mainValue) mainValue.textContent = `${stats.purchases.today.toLocaleString()} ج.م`;
        if (subValue) subValue.textContent = `مشتريات الشهر: ${stats.purchases.month.toLocaleString()} ج.م`;
    }
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP'
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

// Export functions for use in other modules
window.GasManagement = {
    showNotification,
    formatCurrency,
    formatDate,
    loadStatistics
};
