{"name": "gas-management-system", "version": "1.0.0", "description": "نظام إدارة بطاقات الغاز والمواعيد - Gas Cards Management System", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never", "install-deps": "npm install"}, "keywords": ["electron", "gas", "management", "arabic", "desktop", "cards", "appointments"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"sqlite3": "^5.1.6", "chart.js": "^4.4.0", "moment": "^2.29.4"}, "build": {"appId": "com.yourcompany.gasmanagement", "productName": "نظام إدارة بطاقات الغاز", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/sqlite3/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}