# نظام إدارة بطاقات الغاز والمواعيد

تطبيق سطح مكتب متكامل لإدارة بطاقات الغاز والمواعيد والعمليات التجارية المختلفة.

## المميزات

### الأقسام الرئيسية:
- 🏠 **الرئيسية** - لوحة التحكم الرئيسية مع الإحصائيات
- 💳 **بطاقات الغاز** - إدارة بطاقات الغاز والتجديد
- 📅 **المواعيد** - جدولة ومتابعة المواعيد
- 👥 **الزبائن** - إدارة قاعدة بيانات العملاء
- 🚛 **الموردين** - إدارة الموردين والشراكات
- 📦 **المخزون** - متابعة المخزون والكميات
- 🛒 **المبيعات** - تسجيل ومتابعة المبيعات
- 🛍️ **المشتريات** - إدارة عمليات الشراء
- 💰 **الديون** - متابعة الديون والمستحقات
- 🏆 **الشهادات** - إدارة الشهادات والتراخيص
- 🚚 **جدول الإرسال** - تنظيم عمليات التوصيل
- ⚙️ **الإعدادات** - تخصيص النظام
- 📊 **لوحة التحكم** - تقارير وإحصائيات متقدمة

### المميزات التقنية:
- ✅ واجهة عربية كاملة
- ✅ تصميم عصري ومتجاوب
- ✅ قاعدة بيانات محلية SQLite
- ✅ إحصائيات وتقارير فورية
- ✅ نظام إشعارات
- ✅ يعمل على جميع أنظمة التشغيل

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

## التثبيت والتشغيل

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. تشغيل التطبيق في وضع التطوير
```bash
npm run dev
```

### 3. تشغيل التطبيق العادي
```bash
npm start
```

### 4. بناء التطبيق للتوزيع
```bash
npm run build
```

## هيكل المشروع

```
gas-management-system/
├── main.js                 # الملف الرئيسي لـ Electron
├── package.json            # تكوين المشروع والتبعيات
├── src/                    # ملفات المصدر
│   ├── index.html          # الواجهة الرئيسية
│   ├── styles/             # ملفات التصميم
│   │   └── main.css        # التصميم الرئيسي
│   └── scripts/            # ملفات JavaScript
│       └── main.js         # المنطق الرئيسي
├── database/               # قاعدة البيانات (سيتم إنشاؤها)
├── assets/                 # الصور والأيقونات
└── dist/                   # ملفات التوزيع (بعد البناء)
```

## الاستخدام

1. **الصفحة الرئيسية**: تعرض نظرة عامة على جميع الإحصائيات والأنشطة
2. **التنقل**: استخدم شريط التنقل العلوي للانتقال بين الأقسام المختلفة
3. **البطاقات الإحصائية**: تعرض معلومات فورية عن حالة النشاط التجاري
4. **الإشعارات**: تظهر في الزاوية العلوية اليمنى

## التطوير المستقبلي

سيتم إضافة المحتوى التفصيلي لكل قسم بناءً على الصور والمتطلبات المحددة:

- [ ] واجهة إدارة بطاقات الغاز
- [ ] نظام إدارة المواعيد
- [ ] قاعدة بيانات العملاء
- [ ] إدارة المخزون
- [ ] نظام المبيعات والمشتريات
- [ ] إدارة الديون
- [ ] نظام الشهادات
- [ ] جدولة التوصيل
- [ ] تقارير متقدمة

## الدعم التقني

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
