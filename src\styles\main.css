/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    overflow-x: hidden;
    direction: rtl;
}

/* Header Styles */
.header {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.logo i {
    color: #ff6b35;
    font-size: 1.5rem;
}

.date-time {
    display: flex;
    flex-direction: column;
    font-size: 0.9rem;
    opacity: 0.8;
}

.user-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.notifications {
    position: relative;
    cursor: pointer;
}

.notifications i {
    font-size: 1.2rem;
}

.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.user-info i {
    font-size: 1.5rem;
}

/* Navigation Styles */
.navigation {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0 2rem;
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    z-index: 999;
}

.nav-menu {
    display: flex;
    list-style: none;
    max-width: 1400px;
    margin: 0 auto;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-menu::-webkit-scrollbar {
    display: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-bottom-color: #ff6b35;
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    border-bottom-color: #ff6b35;
}

.nav-item i {
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    margin-top: 140px;
    padding: 2rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dashboard-header p {
    opacity: 0.8;
    font-size: 1.1rem;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-card.sales {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.stat-card.items {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.stat-card.appointments {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

.stat-card.orders {
    background: linear-gradient(135deg, #FF9800, #F57C00);
}

.stat-card.suppliers {
    background: linear-gradient(135deg, #607D8B, #455A64);
}

.stat-card.customers {
    background: linear-gradient(135deg, #FF5722, #D84315);
}

.stat-card.debts {
    background: linear-gradient(135deg, #E91E63, #C2185B);
}

.stat-card.purchases {
    background: linear-gradient(135deg, #795548, #5D4037);
}

.card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-info h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.main-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
}

.sub-value {
    font-size: 0.9rem;
    opacity: 0.8;
}

.card-icon {
    font-size: 2.5rem;
    opacity: 0.7;
}

/* Bottom Sections */
.bottom-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.section-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-card h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
    color: #ff6b35;
}

.chart-placeholder {
    height: 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
}

.appointments-list,
.renewal-cards {
    padding: 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-direction: column;
        height: auto;
    }
    
    .main-content {
        margin-top: 200px;
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .bottom-sections {
        grid-template-columns: 1fr;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
