/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    color: #ffffff;
    overflow-x: hidden;
    direction: rtl;
    min-height: 100vh;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    padding: 1rem 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon i {
    color: #fff;
    font-size: 1.8rem;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.main-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.2rem;
}

.date-info {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
}

.user-section {
    display: flex;
    align-items: center;
}

.welcome-badge {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.9rem;
    color: #fff;
}

/* Navigation Styles */
.navigation {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    padding: 0 2rem;
    position: fixed;
    top: 100px;
    left: 0;
    right: 0;
    z-index: 999;
}

.nav-menu {
    display: flex;
    list-style: none;
    max-width: 1400px;
    margin: 0 auto;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    justify-content: space-between;
}

.nav-menu::-webkit-scrollbar {
    display: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
    position: relative;
    font-size: 0.9rem;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-bottom-color: #00bcd4;
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    border-bottom-color: #00bcd4;
}

.nav-item i:first-child {
    font-size: 1rem;
    margin-left: 0.3rem;
}

.nav-item i.fa-caret-down {
    font-size: 0.8rem;
    margin-right: 0.3rem;
    opacity: 0.7;
}

/* Main Content */
.main-content {
    margin-top: 160px;
    padding: 2rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #ffffff;
    font-weight: 600;
}

.dashboard-header p {
    opacity: 0.8;
    font-size: 1.1rem;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-card.sales {
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
}

.stat-card.items {
    background: linear-gradient(135deg, #5C6BC0, #7986CB);
}

.stat-card.appointments {
    background: linear-gradient(135deg, #7E57C2, #9575CD);
}

.stat-card.orders {
    background: linear-gradient(135deg, #42A5F5, #64B5F6);
}

.stat-card.suppliers {
    background: linear-gradient(135deg, #78909C, #90A4AE);
}

.stat-card.customers {
    background: linear-gradient(135deg, #FF7043, #FF8A65);
}

.stat-card.debts {
    background: linear-gradient(135deg, #EC407A, #F06292);
}

.stat-card.purchases {
    background: linear-gradient(135deg, #AB47BC, #BA68C8);
}

.card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-info h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.main-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
}

.sub-value {
    font-size: 0.9rem;
    opacity: 0.8;
}

.card-icon {
    font-size: 2.5rem;
    opacity: 0.7;
}

/* Bottom Sections */
.bottom-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.section-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-card h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
    color: #ff6b35;
}

.chart-placeholder {
    height: 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

.chart-content i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.appointments-list,
.renewal-cards {
    padding: 1rem;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-state {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.empty-state i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
    opacity: 0.5;
}

.statistics-section {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(102, 187, 106, 0.1));
}

.appointments-section {
    background: linear-gradient(135deg, rgba(92, 107, 192, 0.2), rgba(121, 134, 203, 0.1));
}

.renewal-section {
    background: linear-gradient(135deg, rgba(255, 112, 67, 0.2), rgba(255, 138, 101, 0.1));
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-direction: column;
        height: auto;
    }
    
    .main-content {
        margin-top: 200px;
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .bottom-sections {
        grid-template-columns: 1fr;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
