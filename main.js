const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');

let mainWindow;

function createWindow() {
  // إنشاء النافذة الرئيسية
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    show: false,
    titleBarStyle: 'default',
    autoHideMenuBar: true
  });

  // تحميل الملف الرئيسي
  mainWindow.loadFile('src/index.html');

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // فتح أدوات المطور في وضع التطوير
    if (process.argv.includes('--dev')) {
      mainWindow.webContents.openDevTools();
    }
  });

  // إغلاق التطبيق عند إغلاق النافذة الرئيسية
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء القائمة العربية
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // إضافة وظيفة جديد
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // إضافة وظيفة حفظ
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            // إضافة نافذة حول التطبيق
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// تهيئة التطبيق
app.whenReady().then(() => {
  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// إغلاق التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// معالجة أحداث IPC للبيانات
ipcMain.handle('get-statistics', async () => {
  // إرجاع بيانات وهمية للاختبار
  return {
    sales: { total: 5000, today: 2000 },
    gasCards: { total: 150, active: 120 },
    appointments: { today: 8, upcoming: 25 },
    customers: { total: 300, new: 15 },
    suppliers: { total: 45, active: 40 },
    inventory: { items: 250, lowStock: 12 },
    purchases: { today: 1500, month: 45000 },
    debts: { total: 25000, overdue: 5000 },
    certificates: { valid: 180, expiring: 15 }
  };
});

ipcMain.handle('get-recent-activities', async () => {
  return [
    { type: 'sale', description: 'بيع بطاقة غاز - العميل أحمد محمد', time: '10:30 ص', amount: 150 },
    { type: 'appointment', description: 'موعد جديد - فحص دوري', time: '11:15 ص', customer: 'سارة أحمد' },
    { type: 'purchase', description: 'شراء بطاقات غاز جديدة', time: '09:45 ص', amount: 5000 },
    { type: 'certificate', description: 'تجديد شهادة - محطة الوقود الرئيسية', time: '08:30 ص' }
  ];
});
